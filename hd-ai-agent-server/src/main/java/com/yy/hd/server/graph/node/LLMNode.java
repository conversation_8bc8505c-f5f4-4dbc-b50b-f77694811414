package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.streaming.StreamingChatGenerator;
import com.yy.hd.model.ChatClientProvider;
import com.yy.hd.server.graph.SourceType;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.service.McpToolService;
import com.yy.hd.server.service.PromptService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 大模型节点
 */
@Component
public class LLMNode implements NodeAction {

    @Resource
    private PromptService promptService;

    @Resource
    private PromptChatMemoryAdvisor promptChatMemoryAdvisor;

    @Resource
    private ChatClientProvider chatClientProvider;

    @Resource
    private McpToolService mcpToolService;

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey()).orElse(null);
        String provider = workflowContext.getProvider();
        String model = workflowContext.getModel();
        String uid = workflowContext.getUid();
        String chatId = workflowContext.getChatId();
        String source = workflowContext.getSource();
        String query = workflowContext.getInput();
        List<String> toolNames = workflowContext.getToolNames();
        String promptKey = null;
        if (workflowContext.getThinkingEnabled()) {
            promptKey = "thinking";
        }


        Flux<ChatResponse> streamResult = null;
        List<Message> messages = new ArrayList<>();
        List<ToolCallback> toolCallbacks = mcpToolService.getTools(toolNames);
        if (CollectionUtils.isEmpty(toolCallbacks)
                && StringUtils.equalsIgnoreCase(source, SourceType.WEB.getSource())) {
            toolCallbacks = mcpToolService.getTools();
        }
        String systemPrompt = promptService.getPrompt(workflowContext);
        if (CollectionUtils.isNotEmpty(toolCallbacks)) {
            messages.add(new SystemMessage(systemPrompt));
            messages.add(new UserMessage(query));
            Prompt prompt = new Prompt(messages);
            streamResult = chatClientProvider.getChatClient(provider, model)
                    .prompt(prompt)
                    .advisors(promptChatMemoryAdvisor)
                    .advisors(a -> {
                        String conversationId = Optional.ofNullable(chatId)
                                .map(chat -> uid + "_" + chatId)
                                .orElse(uid);
                        a.param(ChatMemory.CONVERSATION_ID, conversationId);
                    })
                    .toolCallbacks(toolCallbacks)
                    .stream()
                    .chatResponse();
        } else {
            messages.add(new SystemMessage(systemPrompt));
            messages.add(new UserMessage(query));
            Prompt prompt = new Prompt(messages);
            streamResult = chatClientProvider.getChatClient(provider, model)
                    .prompt(prompt)
                    .stream()
                    .chatResponse();
        }
        var generator = StreamingChatGenerator.builder()
                .startingNode("LLMNode")
                .startingState(state)
                .mapResult(response -> Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(),
                        Objects.requireNonNull(response.getResult().getOutput().getText())))
                .build(streamResult);
        return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), generator);
    }
}
